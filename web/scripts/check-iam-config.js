#!/usr/bin/env node

/**
 * IAM配置检查脚本
 * 用于验证当前环境的IAM配置是否正确
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
}

// IAM环境配置
const IAM_ENVIRONMENTS = {
  production: {
    iamUrl: 'https://iam.cec.com.cn/cas/login',
    environment: 'production',
    description: '正式环境'
  },
  test: {
    iamUrl: 'https://iam-test.cec.com.cn/cas/login',
    environment: 'test',
    description: '测试环境'
  },
  development: {
    iamUrl: 'https://iam-test.cec.com.cn/cas/login',
    environment: 'development',
    description: '开发环境'
  }
}

function getCurrentIAMConfig() {
  // 优先使用环境变量
  if (process.env.NEXT_PUBLIC_IAM_URL) {
    return {
      iamUrl: process.env.NEXT_PUBLIC_IAM_URL,
      environment: process.env.NODE_ENV || 'production',
      description: `自定义环境 (${process.env.NODE_ENV || 'production'})`
    }
  }

  // 根据NODE_ENV获取配置
  const nodeEnv = process.env.NODE_ENV || 'production'
  return IAM_ENVIRONMENTS[nodeEnv] || IAM_ENVIRONMENTS.production
}

function checkDockerfiles() {
  log.title('🐳 检查Dockerfile配置')
  
  const dockerfiles = [
    { file: 'Dockerfile.prod', env: 'production', expectedUrl: 'https://iam.cec.com.cn/cas/login' },
    { file: 'Dockerfile.test', env: 'test', expectedUrl: 'https://iam-test.cec.com.cn/cas/login' }
  ]

  dockerfiles.forEach(({ file, env, expectedUrl }) => {
    const dockerfilePath = path.join(__dirname, '..', file)
    
    if (!fs.existsSync(dockerfilePath)) {
      log.error(`${file} 不存在`)
      return
    }

    const content = fs.readFileSync(dockerfilePath, 'utf8')
    const iamUrlMatch = content.match(/ENV NEXT_PUBLIC_IAM_URL=(.+)/)
    
    if (!iamUrlMatch) {
      log.error(`${file} 中未找到 NEXT_PUBLIC_IAM_URL 环境变量`)
      return
    }

    const actualUrl = iamUrlMatch[1].trim()
    if (actualUrl === expectedUrl) {
      log.success(`${file} - IAM URL 配置正确: ${actualUrl}`)
    } else {
      log.error(`${file} - IAM URL 配置错误`)
      log.error(`  期望: ${expectedUrl}`)
      log.error(`  实际: ${actualUrl}`)
    }
  })
}

function checkCurrentConfig() {
  log.title('⚙️  当前运行时配置')
  
  const config = getCurrentIAMConfig()
  
  log.info(`环境: ${config.description}`)
  log.info(`NODE_ENV: ${process.env.NODE_ENV || '未设置'}`)
  log.info(`NEXT_PUBLIC_IAM_URL: ${process.env.NEXT_PUBLIC_IAM_URL || '未设置'}`)
  log.info(`IAM URL: ${config.iamUrl}`)
  
  // 验证URL格式
  try {
    new URL(config.iamUrl)
    log.success('IAM URL 格式正确')
  } catch (error) {
    log.error('IAM URL 格式错误')
  }
}

function checkBuildScript() {
  log.title('🔨 检查构建脚本')
  
  const buildScriptPath = path.join(__dirname, '..', '..', 'build-web.sh')
  
  if (!fs.existsSync(buildScriptPath)) {
    log.error('build-web.sh 不存在')
    return
  }

  const content = fs.readFileSync(buildScriptPath, 'utf8')
  
  // 检查是否支持环境参数
  if (content.includes('ENV=${1:-prod}')) {
    log.success('构建脚本支持环境参数')
  } else {
    log.warning('构建脚本可能不支持环境参数')
  }

  // 检查Dockerfile选择逻辑
  if (content.includes('Dockerfile.test') && content.includes('Dockerfile.prod')) {
    log.success('构建脚本支持多环境Dockerfile')
  } else {
    log.warning('构建脚本可能不支持多环境Dockerfile')
  }
}

function showUsageExamples() {
  log.title('📖 使用示例')
  
  console.log(`
${colors.bright}构建命令:${colors.reset}
  # 构建测试环境
  ./build-web.sh test
  
  # 构建正式环境
  ./build-web.sh prod

${colors.bright}环境变量设置:${colors.reset}
  # 自定义IAM URL
  export NEXT_PUBLIC_IAM_URL=https://custom-iam.example.com/cas/login
  
  # 设置环境
  export NODE_ENV=test

${colors.bright}Docker运行时环境变量:${colors.reset}
  docker run -e NEXT_PUBLIC_IAM_URL=https://iam-test.cec.com.cn/cas/login your-image

${colors.bright}验证配置:${colors.reset}
  # 在浏览器控制台执行
  console.log('IAM配置:', window.__NEXT_DATA__.props.pageProps.iamConfig)
  `)
}

// 主函数
function main() {
  console.log(`${colors.bright}${colors.magenta}🔐 IAM配置检查工具${colors.reset}\n`)
  
  checkCurrentConfig()
  checkDockerfiles()
  checkBuildScript()
  showUsageExamples()
  
  console.log(`\n${colors.bright}检查完成！${colors.reset}`)
}

// 运行检查
if (require.main === module) {
  main()
}

module.exports = {
  getCurrentIAMConfig,
  IAM_ENVIRONMENTS
}

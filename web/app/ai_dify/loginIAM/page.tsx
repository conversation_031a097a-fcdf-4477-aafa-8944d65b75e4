'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { getUserInfo, handleIAMError, isValidTicket } from '@/service/iam-auth'
import {
  setUserInfoCookie,
  getUserInfoCookie,
  getQueryParams,
  parseUrlParams,
  redirectToTarget,
  redirectToIAM,
  IAM_CONFIG
} from '@/utils/iam-auth'
import Loading from '@/app/components/base/loading'
import Toast from '@/app/components/base/toast'

interface AuthState {
  isLoading: boolean
  error: string | null
  retryCount: number
}

export default function IAMLoginPage() {
  const searchParams = useSearchParams()
  
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    error: null,
    retryCount: 0
  })

  // 处理ticket获取用户信息
  const processTicket = async (ticket: string) => {
    try {
      console.log('处理ticket:', ticket)
      
      // 验证ticket格式
      if (!isValidTicket(ticket)) {
        throw new Error('无效的ticket格式')
      }
      
      const query = getQueryParams()
      // callback URL包含当前页面的参数信息
      const callbackUrl = `${window.location.origin}/ai_dify/loginIAM${query.params ? `?params=${query.params}` : ''}`
      
      const result = await getUserInfo(ticket, callbackUrl)

      if (result.success && result.userInfo && result.userId) {
        console.log('获取用户信息成功:', result.userInfo)

        // 缓存完整的用户信息
        setUserInfoCookie(result.userInfo, IAM_CONFIG.cookieExpires)

        // 解析参数并跳转
        const { redirectUrl, otherParams } = parseUrlParams(IAM_CONFIG.defaultRedirectPath)
        console.log('准备跳转到:', redirectUrl, otherParams)

        // 显示成功提示
        Toast.notify({
          type: 'success',
          message: `认证成功，欢迎 ${result.userInfo.realName}！正在跳转...`
        })

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          redirectToTarget(redirectUrl, otherParams)
        }, 1000)
      } else {
        throw new Error(result.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('处理ticket失败:', error)
      const errorMessage = handleIAMError(error)
      
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      Toast.notify({
        type: 'error',
        message: errorMessage
      })
    }
  }

  // 核心认证处理逻辑
  const handleAuth = async () => {
    try {
      console.log('开始认证流程')
      
      setAuthState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }))
      
      // 1. 检查缓存的用户信息
      const cachedUserInfo = getUserInfoCookie()
      if (cachedUserInfo) {
        console.log('发现缓存的用户信息:', cachedUserInfo)

        const { redirectUrl, otherParams } = parseUrlParams(IAM_CONFIG.defaultRedirectPath)
        console.log('使用缓存用户信息，跳转到:', redirectUrl)

        Toast.notify({
          type: 'success',
          message: `欢迎回来，${cachedUserInfo.realName}！正在跳转...`
        })

        setTimeout(() => {
          redirectToTarget(redirectUrl, otherParams)
        }, 500)
        return
      }
      
      // 2. 检查是否有ticket参数
      const ticket = searchParams.get('ticket')
      
      if (ticket) {
        console.log('发现ticket参数，处理IAM回调')
        // 3. 处理IAM回调
        await processTicket(ticket)
      } else {
        console.log('无ticket参数，重定向到IAM认证')
        // 4. 重定向到IAM认证
        const { redirectUrl, otherParams } = parseUrlParams(IAM_CONFIG.defaultRedirectPath)
        
        Toast.notify({
          type: 'info',
          message: '正在跳转到IAM认证...'
        })
        
        setTimeout(() => {
          redirectToIAM(redirectUrl, otherParams)
        }, 1000)
      }
    } catch (error) {
      console.error('认证处理失败:', error)
      const errorMessage = handleIAMError(error)
      
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      Toast.notify({
        type: 'error',
        message: errorMessage
      })
    }
  }

  // 重试认证
  const retryAuth = () => {
    if (authState.retryCount < IAM_CONFIG.maxRetries) {
      setAuthState(prev => ({ 
        ...prev, 
        retryCount: prev.retryCount + 1,
        error: null 
      }))
      handleAuth()
    } else {
      Toast.notify({
        type: 'error',
        message: '重试次数已达上限，请刷新页面重试'
      })
    }
  }

  // 页面加载时开始认证
  useEffect(() => {
    handleAuth()
  }, []) // 空依赖数组，只在组件挂载时执行一次

  // 加载状态
  if (authState.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <Loading />
          <div className="text-gray-600 text-lg">认证中，请稍候...</div>
          <div className="text-gray-400 text-sm">正在验证您的身份信息</div>
        </div>
      </div>
    )
  }

  // 错误状态
  if (authState.error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">认证失败</h2>
            <p className="text-gray-600 mb-6">{authState.error}</p>
            
            <div className="space-y-3">
              <button 
                onClick={retryAuth}
                disabled={authState.retryCount >= IAM_CONFIG.maxRetries}
                className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${
                  authState.retryCount >= IAM_CONFIG.maxRetries
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {authState.retryCount >= IAM_CONFIG.maxRetries 
                  ? '重试次数已达上限' 
                  : `重试 (${authState.retryCount}/${IAM_CONFIG.maxRetries})`
                }
              </button>
              
              <button 
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 rounded-lg font-medium text-gray-600 border border-gray-300 hover:bg-gray-50 transition-colors"
              >
                刷新页面
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 正常情况下不应该渲染任何内容（会自动跳转）
  return null
}

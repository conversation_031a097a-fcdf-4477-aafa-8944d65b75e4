#!/usr/bin/env node

/**
 * IAM API接口测试脚本
 * 用于验证IAM接口调用是否正确（不带/console/api前缀）
 */

const http = require('http')
const https = require('https')
const url = require('url')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
}

/**
 * 测试IAM接口调用
 */
async function testIAMAPI() {
  log.title('🔐 IAM API接口测试')
  
  // 测试参数
  const testTicket = 'ST-1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const testCallback = 'https://example.com/callback'
  
  // 构建测试URL
  const testUrl = `/ai_dify/getUserInfo?ticket=${encodeURIComponent(testTicket)}&callback=${encodeURIComponent(testCallback)}`
  
  log.info(`测试URL: ${testUrl}`)
  log.info(`测试ticket: ${testTicket}`)
  log.info(`测试callback: ${testCallback}`)
  
  // 模拟前端调用
  console.log('\n📝 前端调用代码示例:')
  console.log(`${colors.cyan}
const response = await fetch('${testUrl}', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include',
})

const data = await response.json()
console.log('IAM接口响应:', data)
${colors.reset}`)

  // 检查URL格式
  log.title('🔍 URL格式检查')
  
  if (testUrl.startsWith('/ai_dify/')) {
    log.success('URL路径正确: 以 /ai_dify/ 开头')
  } else {
    log.error('URL路径错误: 应该以 /ai_dify/ 开头')
  }
  
  if (testUrl.includes('/console/api/')) {
    log.error('URL包含不应该有的前缀: /console/api/')
  } else {
    log.success('URL不包含 /console/api/ 前缀')
  }
  
  if (testUrl.includes('ticket=') && testUrl.includes('callback=')) {
    log.success('URL包含必要的参数: ticket 和 callback')
  } else {
    log.error('URL缺少必要的参数')
  }

  // 参数编码检查
  log.title('🔧 参数编码检查')
  
  const urlObj = new URL(`http://localhost${testUrl}`)
  const ticketParam = urlObj.searchParams.get('ticket')
  const callbackParam = urlObj.searchParams.get('callback')
  
  if (ticketParam === testTicket) {
    log.success(`ticket参数编码正确: ${ticketParam}`)
  } else {
    log.error(`ticket参数编码错误: 期望 ${testTicket}, 实际 ${ticketParam}`)
  }
  
  if (callbackParam === testCallback) {
    log.success(`callback参数编码正确: ${callbackParam}`)
  } else {
    log.error(`callback参数编码错误: 期望 ${testCallback}, 实际 ${callbackParam}`)
  }

  // 网络请求测试（如果本地服务器运行）
  log.title('🌐 网络请求测试')
  
  try {
    // 尝试连接本地服务器
    const testResponse = await testLocalRequest(testUrl)
    if (testResponse) {
      log.info('本地服务器响应测试完成')
    }
  } catch (error) {
    log.warning('本地服务器未运行或无法连接，跳过网络测试')
    log.info('提示: 启动开发服务器后可进行完整测试')
  }

  log.title('📋 测试总结')
  log.success('IAM接口URL格式正确')
  log.success('参数编码正确')
  log.success('不包含不必要的API前缀')
  log.info('可以在浏览器开发者工具中验证实际请求')
}

/**
 * 测试本地请求
 */
function testLocalRequest(testUrl) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: testUrl,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 5000
    }

    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        log.info(`服务器响应状态: ${res.statusCode}`)
        if (res.statusCode === 404) {
          log.warning('接口返回404，这是正常的（后端接口未实现）')
        } else if (res.statusCode >= 200 && res.statusCode < 300) {
          log.success('接口调用成功')
        } else {
          log.warning(`接口返回状态码: ${res.statusCode}`)
        }
        resolve(data)
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('请求超时'))
    })

    req.end()
  })
}

// 运行测试
if (require.main === module) {
  testIAMAPI().catch(console.error)
}

module.exports = { testIAMAPI }

'use client'
import { FC } from 'react'
import useIAMUserInfo from '@/hooks/use-iam-user-info'
import useUnifiedUserInfo from '@/hooks/use-unified-user-info'

interface UserInfoDisplayProps {
  showIAMOnly?: boolean
  showUnified?: boolean
}

/**
 * 用户信息显示组件 - 演示如何使用新的用户信息hooks
 */
const UserInfoDisplay: FC<UserInfoDisplayProps> = ({ 
  showIAMOnly = false, 
  showUnified = true 
}) => {
  // IAM专用hook
  const iamUserInfo = useIAMUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('IAM用户信息变化:', userInfo)
    },
    onLoginStatusChange: (isLoggedIn) => {
      console.log('IAM登录状态变化:', isLoggedIn)
    }
  })

  // 统一用户信息hook
  const unifiedUserInfo = useUnifiedUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('统一用户信息变化:', userInfo)
    },
    preferIAM: true
  })

  if (showIAMOnly) {
    return (
      <div className="p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-3">IAM用户信息</h3>
        {iamUserInfo.isLoading ? (
          <div>加载中...</div>
        ) : iamUserInfo.isLoggedIn ? (
          <div className="space-y-2">
            <div><strong>真实姓名:</strong> {iamUserInfo.realName}</div>
            <div><strong>用户名:</strong> {iamUserInfo.userId}</div>
            <div><strong>员工编码:</strong> {iamUserInfo.userInfo?.staffCode}</div>
            <div><strong>手机号:</strong> {iamUserInfo.userInfo?.phone}</div>
            <div><strong>部门:</strong> {iamUserInfo.userInfo?.pkDept}</div>
            <button 
              onClick={() => iamUserInfo.logout()}
              className="mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              登出
            </button>
          </div>
        ) : (
          <div>未登录</div>
        )}
      </div>
    )
  }

  if (showUnified) {
    return (
      <div className="p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-3">统一用户信息</h3>
        {unifiedUserInfo.isLoading ? (
          <div>加载中...</div>
        ) : unifiedUserInfo.hasUserInfo ? (
          <div className="space-y-2">
            <div><strong>数据来源:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-sm ${
                unifiedUserInfo.isFromIAM ? 'bg-green-100 text-green-800' :
                unifiedUserInfo.isFromURL ? 'bg-blue-100 text-blue-800' :
                unifiedUserInfo.isFromCache ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {unifiedUserInfo.isFromIAM ? 'IAM认证' :
                 unifiedUserInfo.isFromURL ? 'URL参数' :
                 unifiedUserInfo.isFromCache ? '本地缓存' : '无'}
              </span>
            </div>
            
            {unifiedUserInfo.userInfo.realName && (
              <div><strong>真实姓名:</strong> {unifiedUserInfo.userInfo.realName}</div>
            )}
            {unifiedUserInfo.userInfo.userName && (
              <div><strong>用户名:</strong> {unifiedUserInfo.userInfo.userName}</div>
            )}
            {unifiedUserInfo.userInfo.employeeNum && (
              <div><strong>员工号:</strong> {unifiedUserInfo.userInfo.employeeNum}</div>
            )}
            {unifiedUserInfo.userInfo.phone && (
              <div><strong>手机号:</strong> {unifiedUserInfo.userInfo.phone}</div>
            )}
            {unifiedUserInfo.userInfo.staffCode && (
              <div><strong>员工编码:</strong> {unifiedUserInfo.userInfo.staffCode}</div>
            )}
            {unifiedUserInfo.userInfo.pkDept && (
              <div><strong>部门:</strong> {unifiedUserInfo.userInfo.pkDept}</div>
            )}
            
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">完整用户信息</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
                {JSON.stringify(unifiedUserInfo.userInfo, null, 2)}
              </pre>
            </details>
          </div>
        ) : (
          <div>无用户信息</div>
        )}
      </div>
    )
  }

  return null
}

export default UserInfoDisplay

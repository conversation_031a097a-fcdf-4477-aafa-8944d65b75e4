/**
 * IAM环境配置
 */

export interface IAMEnvironmentConfig {
  iamUrl: string
  environment: 'production' | 'test' | 'development'
  description: string
}

// IAM环境配置映射
export const IAM_ENVIRONMENTS: Record<string, IAMEnvironmentConfig> = {
  production: {
    iamUrl: 'https://iam.cec.com.cn/cas/login',
    environment: 'production',
    description: '正式环境'
  },
  test: {
    iamUrl: 'https://iam-test.cec.com.cn/cas/login',
    environment: 'test',
    description: '测试环境'
  },
  development: {
    iamUrl: 'https://iam-test.cec.com.cn/cas/login', // 开发环境使用测试环境的IAM
    environment: 'development',
    description: '开发环境'
  }
}

/**
 * 获取当前环境的IAM配置
 */
export const getCurrentIAMConfig = (): IAMEnvironmentConfig => {
  // 优先使用环境变量指定的URL
  if (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_IAM_URL) {
    const customUrl = process.env.NEXT_PUBLIC_IAM_URL
    const nodeEnv = process.env.NODE_ENV || 'production'
    
    return {
      iamUrl: customUrl,
      environment: nodeEnv as 'production' | 'test' | 'development',
      description: `自定义环境 (${nodeEnv})`
    }
  }

  // 根据NODE_ENV获取对应配置
  const nodeEnv = typeof process !== 'undefined' ? process.env.NODE_ENV : 'production'
  const config = IAM_ENVIRONMENTS[nodeEnv] || IAM_ENVIRONMENTS.production

  return config
}

/**
 * 获取IAM登录URL
 */
export const getIAMLoginUrl = (): string => {
  return getCurrentIAMConfig().iamUrl
}

/**
 * 打印当前IAM配置信息（用于调试）
 */
export const logIAMConfig = () => {
  if (typeof console !== 'undefined') {
    const config = getCurrentIAMConfig()
    console.log('🔐 IAM配置信息:', {
      环境: config.description,
      IAM地址: config.iamUrl,
      NODE_ENV: typeof process !== 'undefined' ? process.env.NODE_ENV : 'unknown',
      自定义URL: typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_IAM_URL : 'none'
    })
  }
}

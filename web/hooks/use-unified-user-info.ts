'use client'
import { useState, useEffect } from 'react'
import { CACHE_KEY } from '@/config/lx'
import { getCurrentUser, type IAMUserInfo } from '@/utils/iam-auth'

export const getQueryParams = () => {
  if (typeof window === 'undefined') return {}
  const search = window.location.search
  const params = new URLSearchParams(search)
  return Object.fromEntries(params.entries())
}

interface UnifiedUserInfo {
  // 基础字段（兼容原有逻辑）
  employeeNum?: string
  phone?: string
  browser_type?: string
  
  // IAM字段
  staffCode?: string
  userName?: string
  realName?: string
  pkOrg?: string
  pkDept?: string
  pkGroup?: string
  employeeStatus?: string
  psnCode?: string
  groupUserName?: string
  ZrUuid?: string
  sort?: string
  
  // 其他动态字段
  [key: string]: any
}

interface UseUnifiedUserInfoProps {
  onUserInfoChange?: (userInfo: UnifiedUserInfo) => void
  preferIAM?: boolean // 是否优先使用IAM认证，默认true
}

// 将IAM用户信息转换为统一格式
const convertIAMUserInfo = (iamUserInfo: IAMUserInfo): UnifiedUserInfo => {
  return {
    employeeNum: iamUserInfo.employeeNum,
    phone: iamUserInfo.phone,
    staffCode: iamUserInfo.staffCode,
    userName: iamUserInfo.userName,
    realName: iamUserInfo.realName,
    pkOrg: iamUserInfo.pkOrg,
    pkDept: iamUserInfo.pkDept,
    pkGroup: iamUserInfo.pkGroup,
    employeeStatus: iamUserInfo.employeeStatus,
    psnCode: iamUserInfo.psnCode,
    groupUserName: iamUserInfo.groupUserName,
    ZrUuid: iamUserInfo.ZrUuid,
    sort: iamUserInfo.sort
  }
}

const useUnifiedUserInfo = (props: UseUnifiedUserInfoProps = {}) => {
  const { onUserInfoChange, preferIAM = true } = props
  const [userInfo, setUserInfo] = useState<UnifiedUserInfo>({})
  const [isLoading, setIsLoading] = useState(true)
  const [source, setSource] = useState<'iam' | 'url' | 'cache' | 'none'>('none')

  const handleChange = (userInfo: UnifiedUserInfo, sourceType: 'iam' | 'url' | 'cache') => {
    onUserInfoChange?.(userInfo)
    setUserInfo(userInfo)
    setSource(sourceType)
    setIsLoading(false)
    
    // 只有非IAM来源的数据才保存到localStorage
    if (sourceType !== 'iam') {
      localStorage.setItem(CACHE_KEY, JSON.stringify(userInfo))
    }
  }

  useEffect(() => {
    const loadUserInfo = () => {
      if (preferIAM) {
        // 优先级1: 尝试从IAM Cookie中获取用户信息
        const { isLoggedIn, userInfo: iamUserInfo } = getCurrentUser()
        if (isLoggedIn && iamUserInfo) {
          console.log('从IAM Cookie获取用户信息:', iamUserInfo)
          const convertedUserInfo = convertIAMUserInfo(iamUserInfo)
          handleChange(convertedUserInfo, 'iam')
          return
        }
      }

      // 优先级2: 从 URL 查询参数中获取用户信息（兼容旧逻辑）
      const queryParams = getQueryParams()
      const { employeeNum, phone, browser_type, ...otherParams } = queryParams
      const newUserInfo: UnifiedUserInfo = {
        employeeNum,
        phone,
        browser_type,
        ...otherParams
      }
      
      // 过滤掉空值
      const filteredUserInfo = Object.fromEntries(
        Object.entries(newUserInfo).filter(([_, value]) => value !== undefined && value !== null && value !== '')
      )
      
      if (Object.keys(filteredUserInfo).length > 0) {
        console.log('从URL参数获取用户信息:', filteredUserInfo)
        handleChange(filteredUserInfo, 'url')
        return
      }

      // 优先级3: 如果没有 URL 参数，尝试从localStorage缓存中获取
      const userInfoCache = localStorage.getItem(CACHE_KEY)
      if (userInfoCache) {
        try {
          const cachedUserInfo = JSON.parse(userInfoCache)
          if (cachedUserInfo && Object.keys(cachedUserInfo).length > 0) {
            console.log('从localStorage缓存获取用户信息:', cachedUserInfo)
            handleChange(cachedUserInfo, 'cache')
            return
          }
        } catch (e) {
          console.error('Failed to parse cached user info:', e)
          localStorage.removeItem(CACHE_KEY)
        }
      }

      // 没有找到任何用户信息
      setIsLoading(false)
      setSource('none')
    }

    loadUserInfo()
  }, [preferIAM])

  return {
    userInfo,
    isLoading,
    source,
    hasUserInfo: Object.keys(userInfo).length > 0,
    isFromIAM: source === 'iam',
    isFromURL: source === 'url',
    isFromCache: source === 'cache'
  }
}

export default useUnifiedUserInfo

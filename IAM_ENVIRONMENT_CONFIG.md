# IAM环境配置自动化方案

## 🎯 问题背景

IAM系统有两个环境：
- **正式环境**: `iam.cec.com.cn`
- **测试环境**: `iam-test.cec.com.cn`

之前需要每次打包前手动修改IAM域名配置，现在通过环境变量自动化解决。

## 🚀 解决方案

### 1. 自动化配置逻辑

代码会按以下优先级自动选择IAM域名：

1. **环境变量** `NEXT_PUBLIC_IAM_URL` (最高优先级)
2. **NODE_ENV自动判断**:
   - `NODE_ENV=test` → 使用 `iam-test.cec.com.cn`
   - `NODE_ENV=production` → 使用 `iam.cec.com.cn`

### 2. Dockerfile配置

已在两个Dockerfile中配置对应的环境变量：

**Dockerfile.prod** (正式环境):
```dockerfile
ENV NODE_ENV=production
ENV NEXT_PUBLIC_IAM_URL=https://iam.cec.com.cn/cas/login
```

**Dockerfile.test** (测试环境):
```dockerfile
ENV NODE_ENV=test
ENV NEXT_PUBLIC_IAM_URL=https://iam-test.cec.com.cn/cas/login
```

### 3. 构建命令

使用现有的构建脚本，无需修改：

```bash
# 构建测试环境 - 自动使用 iam-test.cec.com.cn
./build-web.sh test

# 构建正式环境 - 自动使用 iam.cec.com.cn  
./build-web.sh prod
```

### 4. 配置验证

运行配置检查脚本验证当前配置：

```bash
cd web
npm run check-iam
```

输出示例：
```
🔐 IAM配置检查工具

⚙️  当前运行时配置
ℹ 环境: 测试环境
ℹ NODE_ENV: test
ℹ NEXT_PUBLIC_IAM_URL: https://iam-test.cec.com.cn/cas/login
ℹ IAM URL: https://iam-test.cec.com.cn/cas/login
✓ IAM URL 格式正确

🐳 检查Dockerfile配置
✓ Dockerfile.prod - IAM URL 配置正确: https://iam.cec.com.cn/cas/login
✓ Dockerfile.test - IAM URL 配置正确: https://iam-test.cec.com.cn/cas/login

🔨 检查构建脚本
✓ 构建脚本支持环境参数
✓ 构建脚本支持多环境Dockerfile
```

## 🛠️ 技术实现

### 核心配置文件

1. **`web/config/iam.ts`** - 环境配置管理
2. **`web/utils/iam-auth.ts`** - IAM认证工具函数
3. **`web/scripts/check-iam-config.js`** - 配置验证脚本

### 配置逻辑

<augment_code_snippet path="web/config/iam.ts" mode="EXCERPT">
````typescript
export const getCurrentIAMConfig = (): IAMEnvironmentConfig => {
  // 优先使用环境变量指定的URL
  if (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_IAM_URL) {
    const customUrl = process.env.NEXT_PUBLIC_IAM_URL
    const nodeEnv = process.env.NODE_ENV || 'production'
    
    return {
      iamUrl: customUrl,
      environment: nodeEnv as 'production' | 'test' | 'development',
      description: `自定义环境 (${nodeEnv})`
    }
  }

  // 根据NODE_ENV获取对应配置
  const nodeEnv = typeof process !== 'undefined' ? process.env.NODE_ENV : 'production'
  const config = IAM_ENVIRONMENTS[nodeEnv] || IAM_ENVIRONMENTS.production

  return config
}
````
</augment_code_snippet>

## 🔧 高级用法

### 运行时自定义IAM地址

```bash
# Docker运行时指定
docker run -e NEXT_PUBLIC_IAM_URL=https://custom-iam.example.com/cas/login your-image

# 本地开发时指定
export NEXT_PUBLIC_IAM_URL=https://custom-iam.example.com/cas/login
npm run dev
```

### 开发环境调试

在开发环境下，会自动打印IAM配置信息：

```javascript
// 浏览器控制台会显示
🔐 IAM配置信息: {
  环境: "测试环境",
  IAM地址: "https://iam-test.cec.com.cn/cas/login",
  NODE_ENV: "development",
  自定义URL: "none"
}
```

## ✅ 优势

- **零手动修改** - 构建时自动选择正确的IAM域名
- **环境隔离** - 测试和正式环境完全分离
- **灵活配置** - 支持环境变量自定义
- **配置验证** - 提供检查工具确保配置正确
- **向后兼容** - 不影响现有构建流程
- **调试友好** - 开发环境自动显示配置信息

## 🔍 故障排除

### 常见问题

1. **配置不生效**
   ```bash
   # 检查当前配置
   npm run check-iam
   ```

2. **环境变量未设置**
   ```bash
   # 检查Dockerfile中的环境变量
   docker inspect your-image | grep NEXT_PUBLIC_IAM_URL
   ```

3. **构建环境错误**
   ```bash
   # 确认使用正确的构建命令
   ./build-web.sh test    # 测试环境
   ./build-web.sh prod    # 正式环境
   ```

## 📝 总结

通过这个自动化方案，您再也不需要手动修改IAM配置了！只需要使用正确的构建命令，系统会自动选择对应环境的IAM域名。

**使用流程**：
1. 开发完成后，选择构建环境
2. 运行对应的构建命令
3. 系统自动使用正确的IAM配置
4. 部署完成！

🎉 **现在您可以专注于业务开发，不再为环境配置烦恼！**
